# UDP Broadcast Protocol Documentation

## Overview

This document describes the UDP broadcast protocol used by sonar devices for device discovery. The sonar devices broadcast their information on port 52102, and the discovery application listens for these packets to detect and display available devices.

## Network Configuration

- **Source Port**: 52100 (LOCAL_PORT) - Port from which sonar device sends broadcasts
- **Destination Port**: 52102 (REMOTE_PORT) - Port on which discovery application listens
- **Protocol**: UDP Broadcast
- **Broadcast Interval**: Configurable delay (typically sent periodically)

## Packet Structure

The UDP packet uses a packed binary structure with 1-byte alignment (`#pragma pack(1)`). The total payload size is 126 bytes (0x7e).

### Packet Header (DFH Header)
| Offset | Field | Size | Value | Description |
|--------|-------|------|-------|-------------|
| 0x00 | headId | 2 bytes | 0x4f53 | Packet header identifier ("SO" in ASCII) |
| 0x02 | srcDeviceId | 2 bytes | 0x1f3c | Source device identifier |
| 0x04 | dstDeviceId | 2 bytes | 0xffff | Destination device ID (broadcast) |
| 0x06 | msgId | 2 bytes | 0x0001 | Message identifier |
| 0x08 | msgVersion | 2 bytes | 0x0000 | Message version |
| 0x0A | payloadSize | 4 bytes | 0x0000007e | Payload size (126 bytes) |
| 0x0E | spare2 | 2 bytes | 0x0408 | Reserved field |

### Device Information
| Offset | Field | Size | Description |
|--------|-------|------|-------------|
| 0x10 | deviceId | 4 bytes | Unique device identifier |
| 0x14 | deviceType | 2 bytes | Device type (0x0001) |
| 0x16 | partNumber | 2 bytes | Part number (PART_NUMBER constant) |
| 0x18 | Status | 4 bytes | Device status (0xd2000cfb) |

### Version Information (3 × 8 bytes each)
Each version info structure contains:
| Field | Size | Description |
|-------|------|-------------|
| firmwareName | 4 bytes | Firmware name (ASCII) |
| mainVer | 1 byte | Major version |
| minorVer | 1 byte | Minor version |
| internalVer | 2 bytes | Internal version |

Version sections:
1. **FPGA Version** (0x1C-0x23): Firmware name "FPGA"
2. **ARM Version** (0x24-0x2B): Firmware name "ARM0"
3. **MCU Version** (0x2C-0x33): Firmware name "MCU0"

### Network Information
| Offset | Field | Size | Description |
|--------|-------|------|-------------|
| 0x34 | ipAddr | 4 bytes | Device IPv4 address |
| 0x38 | ipMask | 4 bytes | Subnet mask |
| 0x3C | connectedIpAddr | 4 bytes | Connected host IP (0 if none) |

### MAC Address
| Offset | Field | Size | Description |
|--------|-------|------|-------------|
| 0x40 | macAddr0-5 | 6 bytes | MAC address bytes |

### Sensor Data
| Offset | Field | Size | Description |
|--------|-------|------|-------------|
| 0x46 | temperature0-7 | 8 × 8 bytes | Eight temperature readings (double precision) |
| 0x86 | Pressure | 8 bytes | Pressure reading (double precision) |

## Byte Ordering

The protocol uses little-endian byte ordering for multi-byte fields.

## Parsing Notes

1. **Header Validation**: Always verify `headId` equals 0x4f53 to confirm valid sonar packet
2. **Device Identification**: Use `deviceId` as unique identifier for device tracking
3. **Network Info**: IP addresses are stored as 4-byte arrays in network byte order
4. **Sensor Values**: Temperature and pressure values are IEEE 754 double precision floats
5. **Version Parsing**: Firmware names are 4-character ASCII strings, versions are numeric

## Example Usage

The discovery application should:
1. Listen on UDP port 52102
2. Validate incoming packets by checking the header ID (0x4f53)
3. Parse the binary structure to extract device information
4. Track devices and implement timeout logic for offline detection
5. Display parsed information in the user interface

## Device Lifecycle

- Devices broadcast continuously while online
- Discovery application should implement timeout (e.g., 30 seconds) to detect offline devices
- `connectedIpAddr` field indicates if device is already connected to another host