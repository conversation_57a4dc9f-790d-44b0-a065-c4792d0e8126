# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a sonar device discovery application built with Electron and TypeScript. The application listens for UDP broadcast packets from sonar devices on port 52102 and displays their information in a web-based interface.

## Architecture

- **Main Process** (`src/main.ts`): Contains the Electron main process with a `DeviceDiscovery` class that:
  - Listens for UDP packets on port 52102
  - Parses binary UDP packets containing device information (IP, MAC, versions, sensor data)
  - Manages discovered devices with a 30-second timeout
  - Provides IPC handlers for the renderer process

- **Renderer Process** (`src/renderer.ts`): Web interface that:
  - Displays discovered devices in a responsive grid layout
  - Shows network info, device info, and sensor data
  - Auto-refreshes every 5 seconds
  - Uses Tailwind CSS for styling

- **Preload Script** (`src/preload.ts`): Exposes secure IPC API to the renderer process

## Development Commands

```bash
# Install dependencies
npm install

# Build the application (CSS, TypeScript, and copy HTML)
npm run build

# Start the application in development mode
npm run dev

# Clean build artifacts
npm run clean

# Package for distribution
npm run pack

# Build distribution packages
npm run dist
```

## Build Process

The build process consists of three steps:
1. `build:css` - Processes CSS with PostCSS and Tailwind
2. `build:ts` - Compiles TypeScript to JavaScript
3. `copy:assets` - Copies HTML files to dist directory

All output goes to the `dist/` directory.

## Key Technical Details

- **UDP Protocol**: Expects packets with specific binary format starting with header ID `0x4f53`
- **Device Timeout**: Devices are removed from the list if not seen for 30 seconds
- **Port Configuration**: Hardcoded to listen on UDP port 52102
- **Packet Structure**: Complex binary format with device info, version data, IP/MAC addresses, and sensor readings (temperatures/pressure)

## Styling

Uses Tailwind CSS v4 with PostCSS processing. Styles are defined in `src/styles.css` and processed through the build pipeline.

## Security

- Context isolation enabled
- Node integration disabled
- Preload script provides controlled API access
- CSP headers configured in HTML