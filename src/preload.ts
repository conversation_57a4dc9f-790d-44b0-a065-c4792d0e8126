import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

contextBridge.exposeInMainWorld('electronAPI', {
  getDevices: () => ipcRenderer.invoke('get-devices'),
  connectToDevice: (ip: string) => ipcRenderer.invoke('connect-to-device', ip),
  disconnectFromDevice: (ip: string) => ipcRenderer.invoke('disconnect-from-device', ip),
  
  onMessage: (callback: (data: any) => void) => {
    ipcRenderer.on('message', (_event, data) => callback(data));
  },
  
  sendMessage: (data: any) => {
    ipcRenderer.send('message', data);
  }
});