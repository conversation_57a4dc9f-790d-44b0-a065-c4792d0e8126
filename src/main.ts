import { app, BrowserWindow, ipcMain } from 'electron';
import * as path from 'path';
import * as dgram from 'dgram';
import * as http from 'http';

interface VersionInfo {
  firmwareName: string;
  mainVer: number;
  minorVer: number;
  internalVer: number;
}

interface UdpPack {
  // DFH 头
  headId: number;      // uint16_t
  srcDeviceId: number; // uint16_t
  dstDeviceId: number; // uint16_t
  msgId: number;       // uint16_t
  msgVersion: number;  // uint16_t
  payloadSize: number; // uint32_t
  spare2: number;      // uint16_t

  // 设备信息
  deviceId: number;    // uint32_t
  deviceType: number;  // uint16_t
  partNumber: number;  // uint16_t
  status: number;      // uint32_t

  // VersionInfo
  fpgaVer: VersionInfo;
  armVer: VersionInfo;
  mcuVer: VersionInfo;

  // IP 信息
  ipAddr: [number, number, number, number];     // uint8_t[4]
  ipMask: [number, number, number, number];     // uint8_t[4]
  connectedIpAddr: [number, number, number, number]; // uint8_t[4]

  // MAC 地址
  macAddr: [number, number, number, number, number, number]; // uint8_t[6]

  // 温度
  temperatures: number[]; // double[8]

  // 压力
  pressure: number; // double
}

interface DeviceInfo {
  // 网络信息
  ip: string;
  mask: string;
  connectedIp: string;
  macAddr: string;
  
  // 设备信息
  deviceId: number;
  deviceType: number;
  partNumber: number;
  status: number;
  
  // 版本信息
  fpgaVer: VersionInfo;
  armVer: VersionInfo;
  mcuVer: VersionInfo;
  
  // 传感器数据
  temperatures: number[];
  pressure: number;
  
  // 系统信息
  lastSeen: Date;
}

class DeviceDiscovery {
  private socket: dgram.Socket | null = null;
  private devices: Map<string, DeviceInfo> = new Map();
  private connectedDevices: Set<string> = new Set();
  private readonly LOCAL_PORT = 52102;
  private readonly EXPECTED_PACKET_SIZE = 126; // UDP packet payload size (0x7e)
  private readonly SONAR_WEB_PORT = 80;
  private loggingEnabled: boolean = false;

  start(): void {
    if (this.socket) return;

    this.socket = dgram.createSocket('udp4');

    this.socket.on('listening', () => {
      console.log('UDP Discovery listening on port', this.LOCAL_PORT);
    });

    this.socket.on('message', (msg) => {
      try {
        const parsedDevice = this.parseUdpPacket(msg);
        if (parsedDevice) {
          this.devices.set(parsedDevice.ip, parsedDevice);
          if (this.loggingEnabled) {
            console.log('Device discovered:', parsedDevice);
          }
        }
      } catch (error) {
        console.error('Error parsing UDP message:', error);
      }
    });

    this.socket.on('error', (err) => {
      console.error('UDP socket error:', err);
    });

    this.socket.bind(this.LOCAL_PORT);
  }

  private parseVersionInfo(buffer: Buffer, offset: number): VersionInfo {
    // All VersionInfo structures follow same format: 4 bytes name + versions
    const firmwareName = buffer.subarray(offset, offset + 4).toString('ascii').replace(/\0/g, '');
    const mainVer = buffer.readUInt8(offset + 4);
    const minorVer = buffer.readUInt8(offset + 5);
    const internalVer = buffer.readUInt16LE(offset + 6);
    
    return {
      firmwareName,
      mainVer,
      minorVer,
      internalVer
    };
  }

  private parseUdpPacket(msg: Buffer): DeviceInfo | null {
    if (this.loggingEnabled) {
      console.log(`Received packet size: ${msg.length} bytes (expected: ${this.EXPECTED_PACKET_SIZE})`);
      console.log(`Full packet hex:`, msg.toString('hex').match(/.{1,2}/g)?.join(' ') || '');
    }
    
    if (msg.length < this.EXPECTED_PACKET_SIZE) {
      if (this.loggingEnabled) {
        console.warn(`Packet too small: ${msg.length} bytes, expected: ${this.EXPECTED_PACKET_SIZE}`);
      }
      return null;
    }

    let offset = 0;

    // 解析DFH头部
    const headId = msg.readUInt16LE(offset); offset += 2;
    
    // 验证包头ID
    if (headId !== 0x4f53) {
      if (this.loggingEnabled) {
        console.warn(`Invalid head ID: 0x${headId.toString(16)}, expected 0x4f53`);
      }
      return null;
    }

    const srcDeviceId = msg.readUInt16LE(offset); offset += 2;
    const dstDeviceId = msg.readUInt16LE(offset); offset += 2;
    const msgId = msg.readUInt16LE(offset); offset += 2;
    const msgVersion = msg.readUInt16LE(offset); offset += 2;
    const payloadSize = msg.readUInt32LE(offset); offset += 4;
    const spare2 = msg.readUInt16LE(offset); offset += 2;

    // 验证payload大小
    if (payloadSize !== 0x7e && this.loggingEnabled) {
      console.warn(`Unexpected payload size: 0x${payloadSize.toString(16)}, expected 0x7e`);
    }

    // 解析设备信息
    const deviceId = msg.readUInt32LE(offset); offset += 4;
    const deviceType = msg.readUInt16LE(offset); offset += 2;
    const partNumber = msg.readUInt16LE(offset); offset += 2;
    const status = msg.readUInt32LE(offset); offset += 4;

    // 解析版本信息 (每个8字节)
    if (this.loggingEnabled) {
      console.log(`Parsing version info at offset: ${offset}`);
    }
    const fpgaVer = this.parseVersionInfo(msg, offset); offset += 8;
    const armVer = this.parseVersionInfo(msg, offset); offset += 8;
    const mcuVer = this.parseVersionInfo(msg, offset); offset += 8;
    if (this.loggingEnabled) {
      console.log(`After version info, offset: ${offset}`);
    }

    // 解析IP信息 (12字节: 4+4+4)
    if (this.loggingEnabled) {
      console.log(`Raw IP bytes at offset ${offset}:`, Array.from(msg.subarray(offset, offset + 12)).map(b => `0x${b.toString(16).padStart(2, '0')}`).join(' '));
    }
    
    const ipAddr: [number, number, number, number] = [
      msg.readUInt8(offset),
      msg.readUInt8(offset + 1),
      msg.readUInt8(offset + 2),
      msg.readUInt8(offset + 3)
    ];

    const ipMask: [number, number, number, number] = [
      msg.readUInt8(offset + 4),
      msg.readUInt8(offset + 5),
      msg.readUInt8(offset + 6),
      msg.readUInt8(offset + 7)
    ];

    const connectedIpAddr: [number, number, number, number] = [
      msg.readUInt8(offset + 8),
      msg.readUInt8(offset + 9),
      msg.readUInt8(offset + 10),
      msg.readUInt8(offset + 11)
    ];
    offset += 12;
    if (this.loggingEnabled) {
      console.log(`Parsed IPs - IP: ${ipAddr.join('.')}, Mask: ${ipMask.join('.')}, Connected: ${connectedIpAddr.join('.')}`);
    }

    // 解析MAC地址 (6字节)
    const macOffset = offset;
    if (this.loggingEnabled) {
      console.log(`Raw MAC bytes at offset ${macOffset}:`, Array.from(msg.subarray(macOffset, macOffset + 6)).map(b => `0x${b.toString(16).padStart(2, '0')}`).join(' '));
    }
    
    const macAddr: [number, number, number, number, number, number] = [
      msg.readUInt8(macOffset),
      msg.readUInt8(macOffset + 1),
      msg.readUInt8(macOffset + 2),
      msg.readUInt8(macOffset + 3),
      msg.readUInt8(macOffset + 4),
      msg.readUInt8(macOffset + 5)
    ];
    
    // 更新offset到MAC之后的位置
    offset += 6;
    if (this.loggingEnabled) {
      console.log(`Parsed MAC: ${macAddr.map(b => b.toString(16).padStart(2, '0')).join(':')}`);
    }

    // 解析温度数据 (8个double值, 每个8字节)
    const temperatures: number[] = [];
    if (this.loggingEnabled) {
      console.log(`Parsing temperatures at offset: ${offset}`);
    }
    
    for (let i = 0; i < 8; i++) {
      if (offset + 8 <= msg.length) {
        temperatures.push(msg.readDoubleLE(offset));
        offset += 8;
      } else {
        if (this.loggingEnabled) {
          console.warn(`Not enough bytes for temperature ${i}. Offset: ${offset}, remaining: ${msg.length - offset}`);
        }
        temperatures.push(0.0);
        break;
      }
    }

    // 解析压力数据 (1个double值, 8字节)
    let pressure = 0.0;
    if (this.loggingEnabled) {
      console.log(`Parsing pressure at offset: ${offset}`);
    }
    
    if (offset + 8 <= msg.length) {
      pressure = msg.readDoubleLE(offset);
      offset += 8;
    } else {
      if (this.loggingEnabled) {
        console.warn(`Not enough bytes for pressure data. Offset: ${offset}, remaining: ${msg.length - offset}`);
      }
    }

    if (this.loggingEnabled) {
      console.log(`Final offset: ${offset}, packet size: ${msg.length}`);
    }

    // 构造DeviceInfo对象
    const device: DeviceInfo = {
      ip: ipAddr.join('.'),
      mask: ipMask.join('.'),
      connectedIp: connectedIpAddr.join('.'),
      macAddr: macAddr.map(b => b.toString(16).padStart(2, '0')).join(':'),
      
      deviceId,
      deviceType,
      partNumber,
      status,
      
      fpgaVer,
      armVer,
      mcuVer,
      
      temperatures,
      pressure,
      
      lastSeen: new Date()
    };

    return device;
  }

  stop(): void {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
  }

  getDevices(): DeviceInfo[] {
    const now = new Date();
    const validDevices: DeviceInfo[] = [];
    
    this.devices.forEach((device, ip) => {
      if (now.getTime() - device.lastSeen.getTime() < 30000) {
        validDevices.push(device);
      } else {
        this.devices.delete(ip);
        this.connectedDevices.delete(ip);
      }
    });

    return validDevices;
  }

  setLoggingEnabled(enabled: boolean): void {
    this.loggingEnabled = enabled;
  }

  isLoggingEnabled(): boolean {
    return this.loggingEnabled;
  }

  async connectToDevice(ip: string): Promise<boolean> {
    try {
      // Validate IP format
      if (!this.isValidIP(ip)) {
        console.error('Invalid IP address format:', ip);
        return false;
      }

      // Check if device exists in discovered devices
      if (!this.devices.has(ip)) {
        console.error('Device not found in discovery list:', ip);
        return false;
      }

      // Test connection to sonar web interface
      const isReachable = await this.testConnection(ip, this.SONAR_WEB_PORT);
      
      if (isReachable) {
        this.connectedDevices.add(ip);
        console.log(`Successfully connected to sonar device at ${ip}:${this.SONAR_WEB_PORT}`);
        return true;
      } else {
        console.error(`Failed to connect to sonar device at ${ip}:${this.SONAR_WEB_PORT}`);
        return false;
      }
    } catch (error) {
      console.error('Error connecting to device:', error);
      return false;
    }
  }

  async disconnectFromDevice(ip: string): Promise<void> {
    this.connectedDevices.delete(ip);
    console.log(`Disconnected from sonar device at ${ip}`);
  }

  private isValidIP(ip: string): boolean {
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipRegex.test(ip);
  }

  private async testConnection(ip: string, port: number, timeout: number = 5000): Promise<boolean> {
    return new Promise((resolve) => {
      const req = http.request({
        hostname: ip,
        port: port,
        method: 'HEAD',
        timeout: timeout
      }, (res) => {
        // Any response (even 404) means the server is reachable
        resolve(true);
      });

      req.on('error', () => {
        resolve(false);
      });

      req.on('timeout', () => {
        req.destroy();
        resolve(false);
      });

      req.end();
    });
  }
}

const deviceDiscovery = new DeviceDiscovery();

function createWindow(): void {
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  mainWindow.loadFile(path.join(__dirname, 'index.html'));

  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }
}

app.whenReady().then(() => {
  createWindow();
  deviceDiscovery.start();
});

ipcMain.handle('get-devices', () => {
  return deviceDiscovery.getDevices();
});

ipcMain.handle('connect-to-device', async (event, ip: string) => {
  return await deviceDiscovery.connectToDevice(ip);
});

ipcMain.handle('disconnect-from-device', async (event, ip: string) => {
  await deviceDiscovery.disconnectFromDevice(ip);
});

app.on('window-all-closed', () => {
  deviceDiscovery.stop();
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});