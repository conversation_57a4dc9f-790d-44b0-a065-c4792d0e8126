<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; frame-src 'self' http: https:; child-src 'self' http: https:; connect-src 'self' http: https:;">
    <title>Demo：声呐设备发现</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body class="font-sans m-0 p-0 bg-gray-900">
    <div id="app" class="h-screen flex flex-col">
        <header class="bg-gray-800 shadow-lg border-b border-gray-700 px-6 py-4">
            <h1 class="text-2xl font-bold text-white">Demo：声呐设备发现</h1>
        </header>
        
        <div class="flex-1 flex overflow-hidden">
            <!-- Device List Panel -->
            <div id="devicePanel" class="w-full bg-gray-800 border-r border-gray-700 overflow-y-auto">
                <div class="p-6">
                    <div class="mb-6 text-center">
                        <button id="refreshBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors shadow-lg">
                            手动刷新
                        </button>
                    </div>
                    
                    <div id="deviceList" class="space-y-4">
                        <p class="text-gray-400 text-center">点击 "手动刷新" 发现网络上的设备…</p>
                    </div>
                </div>
            </div>
            
            <!-- Tiles Panel -->
            <div id="tilesPanel" class="bg-gray-900 flex flex-col hidden">
                <div class="bg-gray-800 border-b border-gray-700 px-4 py-3 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <span id="connectionCount" class="text-sm text-gray-400">0 个连接</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button id="closeAllTiles" class="text-red-400 hover:text-red-300 text-sm" title="关闭所有">
                            全部关闭
                        </button>
                    </div>
                </div>
                <div id="tilesContainer" class="flex-1 p-4 grid gap-4 tiles-grid">
                    <!-- 动态生成的瓦片将在这里显示 -->
                </div>
            </div>
        </div>
    </div>
    <script src="renderer.js"></script>
</body>
</html>